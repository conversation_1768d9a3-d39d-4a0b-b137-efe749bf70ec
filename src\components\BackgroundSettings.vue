<script setup lang="ts">
import type { BackgroundConfig } from '../types/interfaces'
import { ElButton, ElCard, ElColorPicker, ElForm, ElFormItem, ElInput, ElInputNumber, ElMessage, ElOption, ElSelect, ElSlider, ElSwitch, ElUpload } from 'element-plus'
import { computed, onMounted, ref, watch } from 'vue'
import { getConfig, saveConfig } from '../utils/ipc'

// 背景配置
const backgroundConfig = ref<BackgroundConfig>({
  type: 'css',
  cssEffect: 'aurora',
  opacity: 0.8,
  speed: 1,
  colors: ['#7877c6', '#4f46e5', '#06b6d4']
})

const isLoading = ref(false)
const previewMode = ref(false)

// CSS 效果选项
const cssEffectOptions = [
  { label: '极光效果', value: 'aurora' },
  { label: '渐变效果', value: 'gradient' },
  { label: '粒子效果', value: 'particles' },
  { label: '波浪效果', value: 'waves' },
  { label: '矩阵效果', value: 'matrix' }
]

// 背景类型选项
const backgroundTypeOptions = [
  { label: 'CSS 效果', value: 'css' },
  { label: '视频背景', value: 'video' },
  { label: '图片背景', value: 'image' },
  { label: '天气背景', value: 'weather' }
]

// 预设配置
const presetConfigs = [
  {
    name: '经典极光',
    config: {
      type: 'css',
      cssEffect: 'aurora',
      opacity: 0.8,
      speed: 1,
      colors: ['#7877c6', '#4f46e5', '#06b6d4']
    }
  },
  {
    name: '科技矩阵',
    config: {
      type: 'css',
      cssEffect: 'matrix',
      opacity: 0.6,
      speed: 1.5,
      colors: ['#00ff00']
    }
  },
  {
    name: '粒子星空',
    config: {
      type: 'css',
      cssEffect: 'particles',
      opacity: 0.7,
      speed: 0.8,
      colors: ['#7877c6', '#4f46e5']
    }
  },
  {
    name: '彩虹渐变',
    config: {
      type: 'css',
      cssEffect: 'gradient',
      opacity: 0.9,
      speed: 2,
      colors: ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57']
    }
  }
]

// 计算属性
const showCssOptions = computed(() => backgroundConfig.value.type === 'css')
const showVideoOptions = computed(() => backgroundConfig.value.type === 'video')
const showImageOptions = computed(() => backgroundConfig.value.type === 'image')
const showWeatherOptions = computed(() => backgroundConfig.value.type === 'weather')

// 加载配置
async function loadConfig() {
  try {
    isLoading.value = true
    const result = await getConfig({ silent: true })
    if (result.success && result.data?.shutdownBackground) {
      backgroundConfig.value = { ...backgroundConfig.value, ...result.data.shutdownBackground }
    }
  } catch (error) {
    console.error('Failed to load background config:', error)
    ElMessage.error('加载配置失败')
  } finally {
    isLoading.value = false
  }
}

// 保存配置
async function saveBackgroundConfig() {
  try {
    isLoading.value = true
    const result = await getConfig({ silent: true })
    const currentConfig = result.success ? result.data : {}
    
    const newConfig = {
      ...currentConfig,
      shutdownBackground: backgroundConfig.value
    }
    
    const saveResult = await saveConfig(newConfig, { silent: true })
    if (saveResult.success) {
      ElMessage.success('背景配置保存成功')
    } else {
      ElMessage.error('保存配置失败')
    }
  } catch (error) {
    console.error('Failed to save background config:', error)
    ElMessage.error('保存配置失败')
  } finally {
    isLoading.value = false
  }
}

// 应用预设配置
function applyPreset(preset: any) {
  backgroundConfig.value = { ...preset.config }
  ElMessage.success(`已应用预设：${preset.name}`)
}

// 重置为默认配置
function resetToDefault() {
  backgroundConfig.value = {
    type: 'css',
    cssEffect: 'aurora',
    opacity: 0.8,
    speed: 1,
    colors: ['#7877c6', '#4f46e5', '#06b6d4']
  }
  ElMessage.success('已重置为默认配置')
}

// 添加颜色
function addColor() {
  if (!backgroundConfig.value.colors) {
    backgroundConfig.value.colors = []
  }
  backgroundConfig.value.colors.push('#ffffff')
}

// 移除颜色
function removeColor(index: number) {
  if (backgroundConfig.value.colors && backgroundConfig.value.colors.length > 1) {
    backgroundConfig.value.colors.splice(index, 1)
  }
}

// 文件上传处理
function handleVideoUpload(file: any) {
  // 这里应该处理文件上传到本地或服务器
  // 现在只是模拟
  backgroundConfig.value.videoUrl = URL.createObjectURL(file.raw)
  return false // 阻止默认上传
}

function handleImageUpload(file: any) {
  // 这里应该处理文件上传到本地或服务器
  // 现在只是模拟
  backgroundConfig.value.imageUrl = URL.createObjectURL(file.raw)
  return false // 阻止默认上传
}

// 监听配置变化，自动保存
watch(backgroundConfig, () => {
  // 可以添加防抖保存
}, { deep: true })

onMounted(() => {
  loadConfig()
})
</script>

<template>
  <div class="background-settings">
    <ElCard class="mb-4" shadow="hover">
      <template #header>
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold">关机确认页面背景设置</h3>
          <div class="flex gap-2">
            <ElButton size="small" @click="previewMode = !previewMode">
              {{ previewMode ? '关闭预览' : '预览效果' }}
            </ElButton>
            <ElButton size="small" type="primary" :loading="isLoading" @click="saveBackgroundConfig">
              保存配置
            </ElButton>
          </div>
        </div>
      </template>

      <ElForm :model="backgroundConfig" label-width="120px">
        <!-- 背景类型选择 -->
        <ElFormItem label="背景类型">
          <ElSelect v-model="backgroundConfig.type" placeholder="选择背景类型">
            <ElOption
              v-for="option in backgroundTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </ElSelect>
        </ElFormItem>

        <!-- CSS 效果选项 -->
        <template v-if="showCssOptions">
          <ElFormItem label="CSS 效果">
            <ElSelect v-model="backgroundConfig.cssEffect" placeholder="选择CSS效果">
              <ElOption
                v-for="option in cssEffectOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </ElSelect>
          </ElFormItem>

          <ElFormItem label="颜色配置">
            <div class="flex flex-col gap-2">
              <div v-for="(color, index) in backgroundConfig.colors" :key="index" class="flex items-center gap-2">
                <ElColorPicker v-model="backgroundConfig.colors![index]" />
                <span class="text-sm text-gray-600">颜色 {{ index + 1 }}</span>
                <ElButton
                  v-if="backgroundConfig.colors!.length > 1"
                  size="small"
                  type="danger"
                  text
                  @click="removeColor(index)"
                >
                  删除
                </ElButton>
              </div>
              <ElButton size="small" type="primary" text @click="addColor">
                添加颜色
              </ElButton>
            </div>
          </ElFormItem>
        </template>

        <!-- 视频背景选项 -->
        <template v-if="showVideoOptions">
          <ElFormItem label="视频文件">
            <ElUpload
              :before-upload="handleVideoUpload"
              accept="video/*"
              :show-file-list="false"
            >
              <ElButton type="primary">选择视频文件</ElButton>
            </ElUpload>
            <div v-if="backgroundConfig.videoUrl" class="mt-2 text-sm text-gray-600">
              已选择视频文件
            </div>
          </ElFormItem>
        </template>

        <!-- 图片背景选项 -->
        <template v-if="showImageOptions">
          <ElFormItem label="图片文件">
            <ElUpload
              :before-upload="handleImageUpload"
              accept="image/*"
              :show-file-list="false"
            >
              <ElButton type="primary">选择图片文件</ElButton>
            </ElUpload>
            <div v-if="backgroundConfig.imageUrl" class="mt-2 text-sm text-gray-600">
              已选择图片文件
            </div>
          </ElFormItem>
        </template>

        <!-- 天气背景选项 -->
        <template v-if="showWeatherOptions">
          <ElFormItem label="城市位置">
            <ElInput v-model="backgroundConfig.weatherLocation" placeholder="输入城市名称" />
          </ElFormItem>
        </template>

        <!-- 通用选项 -->
        <ElFormItem label="透明度">
          <ElSlider
            v-model="backgroundConfig.opacity"
            :min="0.1"
            :max="1"
            :step="0.1"
            :format-tooltip="(val: number) => `${Math.round(val * 100)}%`"
          />
        </ElFormItem>

        <ElFormItem v-if="showCssOptions" label="动画速度">
          <ElSlider
            v-model="backgroundConfig.speed"
            :min="0.1"
            :max="3"
            :step="0.1"
            :format-tooltip="(val: number) => `${val}x`"
          />
        </ElFormItem>
      </ElForm>
    </ElCard>

    <!-- 预设配置 -->
    <ElCard shadow="hover">
      <template #header>
        <h3 class="text-lg font-semibold">预设配置</h3>
      </template>

      <div class="grid grid-cols-2 gap-4 md:grid-cols-4">
        <div
          v-for="preset in presetConfigs"
          :key="preset.name"
          class="cursor-pointer rounded-lg border-2 border-gray-200 p-3 text-center transition-all hover:border-blue-400 hover:shadow-md"
          @click="applyPreset(preset)"
        >
          <div class="text-sm font-medium">{{ preset.name }}</div>
          <div class="mt-1 text-xs text-gray-500">点击应用</div>
        </div>
      </div>

      <div class="mt-4 flex justify-center">
        <ElButton @click="resetToDefault">重置为默认</ElButton>
      </div>
    </ElCard>
  </div>
</template>

<style scoped>
.background-settings {
  max-width: 800px;
}
</style>
